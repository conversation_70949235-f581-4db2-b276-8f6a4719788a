<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class FPMPReservationSystemTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'member',
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Create FPMP test field
        $this->field = Field::factory()->create([
            'name' => 'FPMP Test Soccer Field',
            'type' => 'Soccer',
            'hourly_rate' => 75.00,
            'capacity' => 22,
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ]);
    }

    #[Test]
    public function user_can_view_reservations_index()
    {
        $this->actingAs($this->user)
            ->get(route('reservations.index'))
            ->assertStatus(200)
            ->assertViewIs('reservations.index')
            ->assertSee('Reservations')
            ->assertSee('New Reservation');
    }

    #[Test]
    public function user_can_view_reservation_creation_form()
    {
        $this->actingAs($this->user)
            ->get(route('reservations.create'))
            ->assertStatus(200)
            ->assertViewIs('reservations.create')
            ->assertSee('New Reservation')
            ->assertSee($this->field->name);
    }

    #[Test]
    public function user_can_create_reservation_successfully()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'special_requests' => 'Need equipment setup',
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'start_time' => $reservationData['start_time'],
            'end_time' => '12:00',
            'duration_hours' => 2,
            'total_cost' => 150.00, // 75 * 2 hours
            'status' => 'Confirmed', // Auto-confirmed in Phase 1
            'customer_name' => 'John Doe',
        ]);

        // Check booking_date separately to handle date format
        $booking = \App\Models\Reservation::where('user_id', $this->user->id)
            ->where('field_id', $this->field->id)
            ->where('start_time', $reservationData['start_time'])
            ->first();
        $this->assertEquals($reservationData['booking_date'], $booking->booking_date->format('Y-m-d'));
    }

    #[Test]
    public function user_cannot_create_reservation_for_past_date()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->subDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData)
            ->assertSessionHasErrors(['booking_date']);
    }

    #[Test]
    public function user_cannot_create_reservation_outside_working_hours()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '23:00', // Outside working hours
            'end_time' => '01:00',
        ];

        $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData)
            ->assertSessionHasErrors(['end_time']);
    }

    #[Test]
    public function user_cannot_create_overlapping_reservations()
    {
        // Create existing reservation
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Try to create overlapping reservation
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '11:00', // Overlaps with existing reservation
            'end_time' => '13:00',
        ];

        $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData)
            ->assertSessionHasErrors(['start_time']);
    }

    #[Test]
    public function user_can_view_their_reservation_details()
    {
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user)
            ->get(route('reservations.show', $reservation))
            ->assertStatus(200)
            ->assertViewIs('reservations.show')
            ->assertSee($reservation->field->name)
            ->assertSee($reservation->status);
    }

    #[Test]
    public function member_can_view_other_users_reservations()
    {
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->create([
            'user_id' => $otherUser->id,
            'field_id' => $this->field->id,
        ]);

        // Members can view all reservations according to current authorization logic
        $this->actingAs($this->user)
            ->get(route('reservations.show', $reservation))
            ->assertStatus(200);
    }

    #[Test]
    public function user_can_edit_their_future_reservation()
    {
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation))
            ->assertStatus(200)
            ->assertViewIs('reservations.edit')
            ->assertSee('Edit Reservation');
    }

    #[Test]
    public function user_can_update_their_reservation()
    {
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'status' => 'Confirmed',
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'start_time' => '14:00',
            'end_time' => '17:00',
            'customer_name' => 'Updated Name',
        ];

        $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData)
            ->assertRedirect(route('reservations.show', $reservation))
            ->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'start_time' => $updateData['start_time'],
            'end_time' => '17:00',
            'duration_hours' => 3,
            'total_cost' => 225.00, // 75 * 3 hours
            'customer_name' => 'Updated Name',
        ]);

        // Check booking_date separately to handle date format
        $reservation->refresh();
        $this->assertEquals($updateData['booking_date'], $reservation->booking_date->format('Y-m-d'));
    }

    #[Test]
    public function user_can_edit_future_reservation()
    {
        $futureDateTime = now()->addHours(12);
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => $futureDateTime->format('Y-m-d'), // Future reservation (24-hour restriction removed)
            'start_time' => $futureDateTime->format('H:i'),
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation))
            ->assertStatus(200)
            ->assertViewIs('reservations.edit');
    }

    #[Test]
    public function user_can_cancel_their_future_reservation()
    {
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user)
            ->post(route('reservations.cancel', $reservation))
            ->assertRedirect()
            ->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => 'Cancelled',
        ]);

        $reservation->refresh();
        $this->assertNotNull($reservation->cancelled_at);
    }



    #[Test]
    public function cost_calculation_is_accurate()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '13:00',
        ];

        $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'duration_hours' => 3,
            'total_cost' => 225.00, // 75 * 3 hours
        ]);
    }

    #[Test]
    public function availability_check_api_works()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.check-availability'), [
                'field_id' => $this->field->id,
                'date' => now()->addDays(1)->format('Y-m-d'),
                'start_time' => '10:00',
                'end_time' => '12:00',
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'available',
                'message',
                'slots',
            ]);
    }

    #[Test]
    public function navigation_links_are_accessible()
    {
        // Test that dashboard redirects to appropriate role-based dashboard
        $this->actingAs($this->user)
            ->get(route('dashboard'))
            ->assertStatus(302)
            ->assertRedirect(route('member.dashboard'));

        // Test that the member dashboard is accessible and has navigation links
        $this->actingAs($this->user)
            ->get(route('member.dashboard'))
            ->assertStatus(200)
            ->assertSee('Reservations')
            ->assertSee('New Reservation');
    }
}
