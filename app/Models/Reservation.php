<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
// /////////////////////////////////////////////////////////////////////
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

// /////////////////////////////////////////////////////////////////////

/**
 * Reservation model for FPMP Phase 1
 * This extends the existing Booking functionality with FPMP-specific features
 */
class Reservation extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     * Using the existing bookings table for Phase 1
     */
    protected $table = 'bookings';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'field_id',
        'user_id',
        'booking_date',
        'start_time',
        'end_time',
        'duration_hours',
        'total_cost',
        'status',
        'customer_name',
        'customer_email',
        'customer_phone',
        'special_requests',
        'confirmed_at',
        'cancelled_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'booking_date' => 'date',
        'duration_hours' => 'decimal:1', // Cast to decimal for half-hour support
        'total_cost' => 'decimal:2',
        'confirmed_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    /**
     * Get the field that owns the reservation
     */
    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Get the user that owns the reservation
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Calculate total cost based on field hourly rate and duration (supports half-hour increments)
     */
    public function calculateTotalCost(): float
    {
        // Use the field's sophisticated cost calculation that handles day/night rates
        // and properly calculates costs for partial hours
        return $this->field->calculateBookingCost($this->start_time, $this->end_time);
    }

    /**
     * Get formatted reservation time range
     */
    public function getTimeRangeAttribute(): string
    {
        return Carbon::parse($this->start_time)->format('H:i').' - '.Carbon::parse($this->end_time)->format('H:i');
    }

    /**
     * Get formatted reservation date and time
     */
    public function getFormattedDateTimeAttribute(): string
    {
        return $this->booking_date->format('M d, Y').' at '.$this->time_range;
    }

    /**
     * Get customer display name
     */
    public function getCustomerDisplayNameAttribute(): string
    {
        if ($this->customer_name) {
            return $this->customer_name;
        }

        return $this->user->name ?? 'Unknown';
    }

    /**
     * Get status badge color for FPMP
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'Pending' => 'warning',
            'Confirmed' => 'secondary',
            'Cancelled' => 'danger',
            'Completed' => 'dark', // Grey styling for completed status
            default => 'secondary',
        };
    }

    /**
     * Get reservation duration in hours (supports half-hour increments)
     */
    public function getDurationInHours(): float
    {
        $start = Carbon::parse($this->start_time);
        $end = Carbon::parse($this->end_time);

        // Calculate duration in minutes and convert to hours with half-hour precision
        $durationMinutes = $start->diffInMinutes($end);

        return round($durationMinutes / 60, 1); // Round to nearest 0.1 hour (6 minutes)
    }

    // //////////////////////////////////////////////////////////////////////////////////////////////////
    /**
     * Get the utilities for the reservation.
     */
    public function utilities(): BelongsToMany
    {
        return $this->belongsToMany(Utility::class)
            ->withPivot('hours', 'rate', 'cost') // if you're storing additional data
            ->withTimestamps();
    }
    // ///////////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * Check if reservation can be cancelled (FPMP Phase 1 rules)
     * Can cancel if status is Pending or Confirmed and reservation is in the future
     */
    public function canBeCancelled(): bool
    {
        // Combine booking_date and start_time into a full datetime
        $reservationDateTime = $this->booking_date->copy()->setTimeFromTimeString($this->start_time);

        return in_array($this->status, ['Pending', 'Confirmed']) && $reservationDateTime->isFuture();
    }

    /**
     * Check if reservation can be un-cancelled (FPMP Phase 1 rules)
     * Only allow uncancelling if currently cancelled and reservation is in the future
     */
    public function canBeUncancelled(): bool
    {
        // Combine booking_date and start_time into a full datetime
        $reservationDateTime = $this->booking_date->copy()->setTimeFromTimeString($this->start_time);

        return $this->isCancelled() && $reservationDateTime->isFuture();
    }

    /**
     * Check if reservation can be modified (FPMP Phase 1 rules)
     * Can modify if status is Pending or Confirmed and reservation is in the future
     */
    public function canBeModified(): bool
    {
        // Combine booking_date and start_time into a full datetime
        $reservationDateTime = $this->booking_date->copy()->setTimeFromTimeString($this->start_time);

        return in_array($this->status, ['Pending', 'Confirmed']) && $reservationDateTime->isFuture();
    }

    /**
     * Validate reservation doesn't conflict with existing ones
     */
    public function hasConflict($excludeId = null): bool
    {

        $query = static::where('field_id', $this->field_id)
            ->where('booking_date', $this->booking_date)
            ->where('status', '!=', 'Cancelled')
            ->where(function ($q) {
                $q->whereBetween('start_time', [$this->start_time, $this->end_time])
                    ->orWhereBetween('end_time', [$this->start_time, $this->end_time])
                    ->orWhere(function ($q2) {
                        $q2->where('start_time', '<=', $this->start_time)
                            ->where('end_time', '>=', $this->end_time);
                    });
            });

        // Exclude a specific reservation id (e.g., when updating the same reservation)
        if (! is_null($excludeId)) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check if reservation duration is valid for the field
     */
    public function hasValidDuration(): bool
    {
        return $this->field->isValidDuration($this->duration_hours);
    }

    /**
     * Check if this reservation has ended (current datetime is past the end datetime)
     */
    public function hasEnded(): bool
    {
        $now = now();
        $endDateTime = $this->booking_date->format('Y-m-d').' '.$this->end_time;

        return $now->greaterThan(Carbon::parse($endDateTime));
    }

    /**
     * Check if reservation is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'Cancelled';
    }

    /**
     * Check if reservation is within field working hours
     */
    public function isWithinWorkingHours(): bool
    {
        return $this->field->isWithinWorkingHours($this->start_time, $this->end_time);
    }

    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /**
     * Check if reservation is within minimum reservation time (FPMP Phase 1 rules)
     */
    public function isWithinMinimumReservationTime(): bool
    {
        // Combine booking_date and start_time into a full datetime
        $reservationDateTime = $this->booking_date->copy()->setTimeFromTimeString($this->start_time);

        // Return true if the reservation starts in less than 60 minutes
        return now()->diffInMinutes($reservationDateTime, false) < 60;
    }
    // //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * Auto-confirm reservation for Phase 1 (no approval workflow)
     */
    public function autoConfirm(): void
    {
        $this->update([
            'status' => 'Confirmed',
            'confirmed_at' => now(),
        ]);
    }

    /**
     * Cancel the reservation
     */
    public function cancel(): void
    {
        $this->update([
            'status' => 'Cancelled',
            'cancelled_at' => now(),
        ]);
    }

    /**
     * Uncancel the reservation
     */
    public function uncancel(): void
    {
        $this->update([
            'status' => 'Confirmed',  // or 'Pending', whichever your default is after uncancelling
            'cancelled_at' => null,
        ]);
    }

    /**
     * Scope for upcoming reservations
     */
    public function scopeUpcoming($query)
    {
        $now = now();

        // Database-agnostic approach: Use Laravel's built-in date/time methods
        // Get all reservations from today onwards, then filter in a collection
        $candidates = $query->where('booking_date', '>=', $now->toDateString())->get();

        // Filter to only include reservations that are truly in the future
        $upcomingIds = $candidates->filter(function ($reservation) use ($now) {
            // Construct the full reservation datetime
            $reservationDateTime = $reservation->booking_date->copy()
                ->setTimeFromTimeString($reservation->start_time);

            // Only include if the reservation datetime is strictly greater than now
            return $reservationDateTime->greaterThan($now);
        })->pluck('id');

        // Return a query that includes only the filtered IDs
        return $query->whereIn('id', $upcomingIds->toArray());
    }

    /**
     * Scope for today's reservations
     */
    public function scopeToday($query)
    {
        return $query->whereDate('booking_date', now()->toDateString());
    }

    /**
     * Scope for user's reservations
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for active reservations (not cancelled)
     */
    public function scopeActive($query)
    {
        return $query->where('status', '!=', 'Cancelled');
    }

    /**
     * Update confirmed reservations that have ended to 'Completed' status
     * This method checks if the current datetime has passed the reservation's end datetime
     * (booking_date + end_time) before marking it as completed.
     */
    public static function updateCompletedReservations(): int
    {
        $now = now();
        $updatedCount = 0;

        // Get all confirmed reservations and check them individually
        // This approach is database-agnostic and works with both MySQL and PostgreSQL
        $confirmedReservations = static::where('status', 'Confirmed')->get();

        foreach ($confirmedReservations as $reservation) {
            // Combine booking_date and end_time into a full datetime using Carbon
            $endDateTime = $reservation->booking_date->copy()->setTimeFromTimeString($reservation->end_time);

            // Check if the reservation has ended
            if ($now->greaterThan($endDateTime)) {
                $reservation->update(['status' => 'Completed']);
                $updatedCount++;
            }
        }

        return $updatedCount;
    }
}
