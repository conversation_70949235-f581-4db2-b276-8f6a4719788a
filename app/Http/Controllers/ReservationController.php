<?php

namespace App\Http\Controllers;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\Utility;
use App\Rules\ActiveUtilityRule;
use App\Services\FieldAvailabilityService;
use App\Services\ReservationCostService;
use App\Services\ReservationValidationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReservationController extends Controller
{
    protected FieldAvailabilityService $availabilityService;

    protected ReservationCostService $costService;

    protected ReservationValidationService $validationService;

    public function __construct(
        FieldAvailabilityService $availabilityService,
        ReservationCostService $costService,
        ReservationValidationService $validationService
    ) {
        $this->availabilityService = $availabilityService;
        $this->costService = $costService;
        $this->validationService = $validationService;
    }

    /**
     * Display a listing of the user's reservations.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $query = Reservation::with('field', 'user');

        // *********** Mark as complete when current datetime is higher than confirmed reservation end datetime *******************//
        // Update all confirmed reservations that have ended to 'Completed'
        // This uses the booking_date + end_time to determine if the reservation has actually finished
        Reservation::updateCompletedReservations();
        // *********** Mark as complete when current datetime is higher than confirmed reservation end datetime *******************//

        // ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range (ensure end date is inclusive)
        if ($request->filled('date_from')) {
            $query->whereDate('booking_date', '>=', Carbon::parse($request->date_from)->toDateString());
        }
        if ($request->filled('date_to')) {
            // Use '<' on the next day to guarantee inclusive behavior regardless of DB driver/timezone
            $inclusiveEnd = Carbon::parse($request->date_to)->addDay()->toDateString();
            $query->whereDate('booking_date', '<', $inclusiveEnd);
        }

        // Filter by field
        if ($request->filled('field_id')) {
            $query->where('field_id', $request->field_id);
        }
        // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        // Apply sorting
        $this->applySorting($query, $request);

        // All users (admin or not) can see all reservations
        $reservations = $query->paginate(10);

        // Get upcoming reservations count for dashboard
        $upcomingCount = Reservation::upcoming()
            ->active()
            ->count();

        // Get total reservations count (unfiltered) for the "Total Reservations" card
        $totalReservationsCount = Reservation::count();

        $fields = Field::active()->orderBy('name')->get();

        return view('reservations.index', compact('reservations', 'upcomingCount', 'totalReservationsCount', 'fields'));
    }

    /**
     * Apply sorting to the reservations query using Laravel best practices
     * and database-agnostic Eloquent methods
     */
    private function applySorting($query, Request $request)
    {
        $sortField = $request->get('sort');
        $direction = $request->get('direction', 'asc');

        // Validate direction against whitelist
        $allowedDirections = ['asc', 'desc'];
        if (! in_array($direction, $allowedDirections)) {
            $direction = 'asc';
        }

        // Validate sort field against whitelist
        $allowedSortFields = ['field', 'date_time', 'customer', 'cost', 'status'];
        if (! in_array($sortField, $allowedSortFields)) {
            // Default sorting: newest first
            return $query->orderBy('booking_date', 'desc')
                ->orderBy('start_time', 'desc');
        }

        switch ($sortField) {
            case 'field':
                // Use subquery ordering to avoid joins and improve database compatibility
                return $query->orderBy(
                    \App\Models\Field::select('name')
                        ->whereColumn('fields.id', 'bookings.field_id')
                        ->limit(1),
                    $direction
                );

            case 'date_time':
                // Sort by booking date first, then start time
                return $query->orderBy('booking_date', $direction)
                    ->orderBy('start_time', $direction);

            case 'customer':
                // Handle both custom customer names and user names using database-agnostic approach
                // Use COALESCE with NULLIF to prioritize customer_name over user.name
                return $query->orderBy(
                    \App\Models\User::selectRaw("COALESCE(NULLIF(bookings.customer_name, ''), users.name)")
                        ->whereColumn('users.id', 'bookings.user_id')
                        ->limit(1),
                    $direction
                );

            case 'cost':
                // Sort by total cost numerically
                return $query->orderBy('total_cost', $direction);

            case 'status':
                // Sort by status with custom priority using database-agnostic approach
                $statusOrder = [
                    'Confirmed' => 1,
                    'Pending' => 2,
                    'Cancelled' => 3,
                    'Completed' => 4,
                ];

                // Build CASE statement with proper parameter binding for security
                $caseStatement = 'CASE bookings.status ';
                $bindings = [];

                foreach ($statusOrder as $status => $priority) {
                    $caseStatement .= 'WHEN ? THEN ? ';
                    $bindings[] = $status;
                    $bindings[] = $priority;
                }
                $caseStatement .= 'ELSE ? END';
                $bindings[] = 5; // Default priority for unknown statuses

                return $query->orderByRaw($caseStatement.' '.$direction, $bindings);

            default:
                // Default sorting: newest first
                return $query->orderBy('booking_date', 'desc')
                    ->orderBy('start_time', 'desc');
        }
    }

    /**
     * Show the form for creating a new reservation.
     */
    public function create(Request $request)
    {
        // ///////////// pa wak si e field ta under maintenance
        $fields1 = Field::all();

        foreach ($fields1 as $field1) {
            $field1->updateStatusBasedOnDates();
        }
        // ////////////////////////////////////////////////////////////////////
        // Get available fields
        $fields = Field::where('status', 'Active')->orderBy('name')->get();

        // Get active utilities
        $utilities = Utility::where('is_active', true)->orderBy('name')->get();

        // Get selected field if provided
        $selectedField = null;
        if ($request->has('field_id')) {
            $selectedField = Field::find($request->field_id);
        }

        // Get selected date if provided (handle both 'date' and 'booking_date' parameters)
        $selectedDate = $request->get('date') ?: $request->get('booking_date', now()->addDay()->format('Y-m-d'));

        // Get selected time if provided
        $selectedTime = $request->get('time') ?: $request->get('start_time');

        // Convert full time format to HH:MM if needed (FullCalendar sends "14:00:00")
        if ($selectedTime && strlen($selectedTime) > 5) {
            $selectedTime = substr($selectedTime, 0, 5); // "14:00:00" -> "14:00"
        }

        // Get selected duration if provided
        $selectedDuration = $request->get('duration_hours', 1);

        // Get field availability for the next 7 days if a field is selected
        $fieldAvailability = [];
        if ($selectedField) {
            $startDate = Carbon::parse($selectedDate);
            $endDate = $startDate->copy()->addDays(6);
            $fieldAvailability = $this->availabilityService->getFieldAvailabilityCalendar($selectedField, $startDate, $endDate);
        }

        return view('reservations.create', compact('fields', 'utilities', 'selectedField', 'selectedDate', 'selectedTime', 'selectedDuration', 'fieldAvailability'));
    }

    /**
     * Store a newly created reservation in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date', 'after_or_equal:today'],
            'start_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'end_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
            'utilities' => ['nullable', 'array'],
            'utilities.*.id' => ['required_with:utilities', new ActiveUtilityRule],
            'utilities.*.hours' => ['required_with:utilities', 'integer', 'min:1'], // Utilities must be whole number quantities
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            return back()->withErrors($validator)->withInput();
        }

        $field = Field::findOrFail($request->field_id);

        // Parse start and end times
        $startTime = Carbon::parse($request->start_time);
        $endTime = Carbon::parse($request->end_time);

        // Validate that end time is after start time
        if ($endTime <= $startTime) {
            $error = [
                'end_time' => 'End time must be after start time.',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // Calculate duration in hours
        $durationHours = $startTime->diffInMinutes($endTime) / 60;

        // Validate working hours
        if (! $field->isWithinWorkingHours($request->start_time, $request->end_time)) {
            $error = [
                'start_time' => 'Reservation must be within field working hours ('.
                               $field->opening_time.' - '.$field->closing_time.')',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // Validate duration
        if (! $field->isValidDuration($durationHours)) {
            $error = [
                'end_time' => 'Duration must be between '.$field->min_booking_hours.
                                   ' and '.$field->max_booking_hours.' hours for this field.',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // ///////////////////////////////////////////////////////////////////////////////////////////////////////////
        // Check pa e no book 60 minutes prome ku e orario (only apply to regular users)
        if ($user->isUser()) {
            // Combine booking_date and start_time into a full datetime
            $reservationDateTime = Carbon::parse($request->booking_date)
                ->setTimeFromTimeString($request->start_time);

            // Prevent booking if reservation starts in less than 60 minutes
            if (now()->diffInMinutes($reservationDateTime, false) < 60) {
                $error = [
                    'start_time' => 'You must book at least 1 hour before the reservation start time.',
                ];
                if ($request->expectsJson()) {
                    return response()->json(['errors' => $error], 422);
                }

                return back()->withErrors($error)->withInput();
            }
        }
        // ///////////////////////////////////////////////////////////////////////////////////////////////////////////

        // Check availability
        if (! $field->isAvailableAt($request->booking_date, $request->start_time, $request->end_time)) {
            $error = [
                'start_time' => 'The selected time slot is not available. Please choose a different time.',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // /////////////////////////////////////////////////////////////////////////////////////////////////////////////
        /* Check if  day is available. Day must not be in the maintenance or vacation period */
        if ($field->vacationOrMaintenance($request->booking_date)) {
            $error = [
                'booking_date' => 'The reservation date must be outside the vacation or maintenance period ('.
                               $field->start_date.' - '.$field->end_date.')',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }
        // /////////////////////////////////////////////////////////////////////////////////////////////////////////////

        // Calculate total cost using server-side authoritative calculation
        $utilities = $request->utilities ?? [];
        $costCalculation = $this->costService->calculateTotalCostWithUtilities(
            $field,
            $durationHours,
            $request->start_time,
            $utilities
        );

        $totalCost = $costCalculation['total_cost'];
        $utilityData = $costCalculation['utility_breakdown'];

        // Create reservation
        $reservation = Reservation::create([
            'field_id' => $request->field_id,
            'user_id' => auth()->id(),
            'booking_date' => $request->booking_date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'duration_hours' => $durationHours,
            'total_cost' => $totalCost,
            'status' => 'Pending',
            'customer_name' => $request->customer_name ?: auth()->user()->name,
            'customer_email' => $request->customer_email,
            'customer_phone' => $request->customer_phone,
            'special_requests' => $request->special_requests,
        ]);

        // /////////////////////////////////////////////////////////////////////////////////////////////////////
        // Save utilities (assuming pivot table `reservation_utility`)
        foreach ($utilityData as $data) {
            $reservation->utilities()->attach($data['utility_id'], [
                'hours' => $data['hours'],
                'rate' => $data['rate'],
                'cost' => $data['cost'],
            ]);
        }
        // /////////////////////////////////////////////////////////////////////////////////////////////////////

        // Auto-confirm for Phase 1 (no approval workflow)
        $reservation->autoConfirm();

        // Send confirmation email
        // Mail::to($reservation->user->email)->send(new ReservationCreatedMail($reservation));
        /*$email = $booking->customer_email ?? $booking->user->email;
        Mail::to($email)->send(new ReservationCreatedMail($booking));
        // Also send mail to all admins
        $admins = User::where('role', 'admin')->pluck('email');
        foreach ($admins as $adminEmail) {
            Mail::to($adminEmail)->send(new ReservationCreatedMail($booking));
        }*/

        return redirect()->route('reservations.show', $reservation)
            ->with('success', 'Reservation created successfully! Your reservation has been confirmed.');
    }

    /**
     * Display the specified reservation.
     */
    public function show(Reservation $reservation)
    {
        $user = auth()->user();

        // Allow admins, members, OR the user who owns the reservation
        if (! $user->isAdmin() && ! $user->isMember() && $reservation->user_id !== auth()->id()) {
            abort(403, 'You can only view your own reservations.');
        }

        $reservation->load('field', 'utilities');

        // If this is an AJAX request, return JSON data
        if (request()->ajax() || request()->wantsJson()) {
            return $this->getReservationDetailsJson($reservation);
        }

        return view('reservations.show', compact('reservation'));
    }

    /**
     * Get reservation details as JSON for AJAX requests
     */
    private function getReservationDetailsJson(Reservation $reservation)
    {
        // Calculate cost breakdown
        $costService = new ReservationCostService;
        $costBreakdown = $costService->getCostBreakdown(
            $reservation->field,
            $reservation->duration_hours,
            $reservation->start_time
        );
        $utilityTotal = $reservation->utilities->sum('pivot.cost');

        $user = auth()->user();

        // Determine if user can modify/cancel based on role and ownership
        $canModify = $reservation->canBeModified() && ($user->isAdmin() || $reservation->user_id === $user->id);
        $canCancel = $reservation->canBeCancelled() && ($user->isAdmin() || $reservation->user_id === $user->id);

        return response()->json([
            'id' => $reservation->id,
            'status' => $reservation->status,
            'status_color' => $reservation->status_color,
            'can_be_modified' => $canModify,
            'can_be_cancelled' => $canCancel,

            // Field information
            'field' => [
                'id' => $reservation->field->id,
                'name' => $reservation->field->name,
                'type' => $reservation->field->type,
                'capacity' => $reservation->field->capacity,
                'hourly_rate' => $reservation->field->hourly_rate,
                'night_hourly_rate' => $reservation->field->night_hourly_rate,
                'description' => $reservation->field->description ?: 'No description available',
                'icon' => $reservation->field->icon ?? 'bx bx-stadium',
            ],

            // Schedule information
            'booking_date' => $reservation->booking_date->format('l, F d, Y'),
            'formatted_booking_date' => $reservation->booking_date->format('l, F d, Y'),
            'time_range' => $reservation->time_range,
            'duration_hours' => $reservation->duration_hours,
            'formatted_date_time' => $reservation->formatted_date_time,
            'is_today' => $reservation->booking_date->isToday(),
            'is_tomorrow' => $reservation->booking_date->isTomorrow(),
            'is_future' => $reservation->booking_date->isFuture(),

            // User information
            'user_name' => $reservation->user->name,

            // Customer information
            'customer_display_name' => $reservation->customer_display_name,
            'customer_email' => $reservation->customer_email ?: 'Not provided',
            'customer_phone' => $reservation->customer_phone ?: 'Not provided',
            'special_requests' => $reservation->special_requests,

            // Cost information
            'total_cost' => $reservation->total_cost,
            'cost_breakdown' => $costBreakdown,
            'utility_total' => $utilityTotal,

            // Utilities
            'utilities' => $reservation->utilities->map(function ($utility) {
                return [
                    'name' => $utility->name,
                    'hours' => $utility->pivot->hours,
                    'rate' => $utility->pivot->rate,
                    'cost' => $utility->pivot->cost,
                    'icon_class' => $utility->icon_class ?? 'ti ti-tool',
                ];
            }),

            // Timeline information
            'created_at' => $reservation->created_at->format('M d, Y H:i'),
            'confirmed_at' => $reservation->confirmed_at?->format('M d, Y H:i'),
            'cancelled_at' => $reservation->cancelled_at?->format('M d, Y H:i'),

            // URLs for actions
            'edit_url' => route('reservations.edit', $reservation),
            'cancel_url' => route('reservations.cancel', $reservation),
            'book_same_field_url' => route('reservations.create', ['field_id' => $reservation->field_id]),
        ]);
    }

    /**
     * Show the form for editing the specified reservation.
     */
    public function edit(Reservation $reservation)
    {
        $user = auth()->user();

        // Authorization check: Allow admins or reservation owners
        if (! $user->isAdmin() && $reservation->user_id !== auth()->id()) {
            abort(403, 'You can only edit your own reservations.');
        }

        // Check if reservation can be modified
        if (! $reservation->canBeModified()) {
            return back()->with('error', 'This reservation cannot be modified. Reservations can only be changed up to 24 hours before the scheduled time.');
        }
        // ////////////////////////////////////////////////////////
        // $fields = Field::all();
        $utilities = Utility::all();

        $reservationUtilities = $reservation->utilities->map(function ($utility) {
            return [
                'id' => $utility->id,
                'name' => $utility->name,
                'rate' => $utility->pivot->rate,
                'hours' => $utility->pivot->hours,
                'cost' => $utility->pivot->cost,
            ];
        });
        // ////////////////////////////////////////////////////////
        $fields = Field::where('status', 'Active')->orderBy('name')->get();

        return view('reservations.edit', compact('reservation', 'fields', 'utilities', 'reservationUtilities'));
    }

    /**
     * Update the specified reservation in storage.
     */
    public function update(Request $request, Reservation $reservation)
    {
        $user = auth()->user();

        // Authorization check: Allow admins or reservation owners
        if (! $user->isAdmin() && $reservation->user_id !== auth()->id()) {
            abort(403, 'You can only edit your own reservations.');
        }

        // Check if reservation can be modified
        if (! $reservation->canBeModified()) {
            return back()->with('error', 'This reservation cannot be modified. Reservations can only be changed up to 24 hours before the scheduled time.');
        }

        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date', 'after_or_equal:today'],
            'start_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'end_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
            'utilities' => ['nullable', 'array'],
            'utilities.*.id' => ['required_with:utilities', new ActiveUtilityRule],
            'utilities.*.hours' => ['required_with:utilities', 'integer', 'min:1'], // Utilities must be whole number quantities
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $field = Field::findOrFail($request->field_id);

        // Parse start and end times
        $startTime = Carbon::parse($request->start_time);
        $endTime = Carbon::parse($request->end_time);

        // Validate that end time is after start time
        if ($endTime <= $startTime) {
            return back()->withErrors([
                'end_time' => 'End time must be after start time.',
            ])->withInput();
        }

        // Calculate duration in hours
        $durationHours = $startTime->diffInMinutes($endTime) / 60;

        // ///////////////////////////////////////////////////////////////////////////////////////////////////////////
        // Check pa e no book 60 minutes prome ku e orario (only apply to regular users) (PRIORITY VALIDATION - check this first)
        if ($user->isUser()) {
            // Combine booking_date and start_time into a full datetime
            $reservationDateTime = Carbon::parse($request->booking_date)
                ->setTimeFromTimeString($request->start_time);

            // Prevent booking if reservation starts in less than 60 minutes
            if (now()->diffInMinutes($reservationDateTime, false) < 60) {
                return back()->withErrors([
                    'start_time' => 'You must book at least 1 hour before the reservation start time.',
                ])->withInput();
            }
        }
        // ///////////////////////////////////////////////////////////////////////////////////////////////////////////

        // Validate working hours
        if (! $field->isWithinWorkingHours($request->start_time, $request->end_time)) {
            return back()->withErrors([
                'start_time' => 'Reservation must be within field working hours ('.
                               $field->opening_time.' - '.$field->closing_time.')',
            ])->withInput();
        }

        // Validate duration
        if (! $field->isValidDuration($durationHours)) {
            return back()->withErrors([
                'end_time' => 'Duration must be between '.$field->min_booking_hours.
                                   ' and '.$field->max_booking_hours.' hours for this field.',
            ])->withInput();
        }

        // Check availability (excluding current reservation)
        if (! $field->isAvailableAt($request->booking_date, $request->start_time, $request->end_time, $reservation->id)) {
            return back()->withErrors([
                'start_time' => 'The selected time slot is not available. Please choose a different time.',
            ])->withInput();
        }

        // /////////////////////////////////////////////////////////////////////////////////////////////////////////////
        /* Check if  day is available. Day must not be in the maintenance or vacation period */
        if ($field->vacationOrMaintenance($request->booking_date)) {
            return back()->withErrors([
                'booking_date' => 'The reservation date must be outside the vacation or maintenance period ('.
                               $field->start_date.' - '.$field->end_date.')',
            ])->withInput();
        }
        // /////////////////////////////////////////////////////////////////////////////////////////////////////////////

        // Calculate total cost using server-side authoritative calculation (consistent with store method)
        $utilities = $request->utilities ?? [];
        $costCalculation = $this->costService->calculateTotalCostWithUtilities(
            $field,
            $durationHours,
            $request->start_time,
            $utilities
        );

        $totalCost = $costCalculation['total_cost'];
        $utilityData = $costCalculation['utility_breakdown'];

        // Update utilities using server-calculated data
        $reservation->utilities()->detach(); // Clear old utilities

        foreach ($utilityData as $utility) {
            $reservation->utilities()->attach($utility['utility_id'], [
                'hours' => $utility['hours'],
                'rate' => $utility['rate'],
                'cost' => $utility['cost'],
            ]);
        }

        // Update reservation
        $reservation->update([
            'field_id' => $request->field_id,
            'booking_date' => $request->booking_date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'duration_hours' => $durationHours,
            'total_cost' => $totalCost,
            'customer_name' => $request->customer_name ?: auth()->user()->name,
            'customer_email' => $request->customer_email,
            'customer_phone' => $request->customer_phone,
            'special_requests' => $request->special_requests,
        ]);

        // send mail
        /*
    // Pick recipient (customer or linked user)
    $email = $reservation->customer_email ?? $reservation->user->email;
    // Send mail to customer
    Mail::to($email)->send(new ReservationCreatedMail($reservation));
    // Send mail to all admins
    $adminEmails = User::where('role', 'admin')->pluck('email')->toArray();
    Mail::to($adminEmails)->send(new ReservationCreatedMail($reservation));
    */
        return redirect()->route('reservations.show', $reservation)
            ->with('success', 'Reservation updated successfully!');
    }

    /**
     * Cancel the specified reservation.
     */
    public function cancel(Reservation $reservation)
    {
        $user = auth()->user();

        // Authorization check: Allow admins or reservation owners
        if (! $user->isAdmin() && $reservation->user_id !== auth()->id()) {
            if (request()->ajax() || request()->expectsJson()) {
                return response()->json(['error' => 'You can only cancel your own reservations.'], 403);
            }
            abort(403, 'You can only cancel your own reservations.');
        }

        if (! $reservation->canBeCancelled()) {
            $errorMessage = 'This reservation cannot be cancelled. Reservations can only be cancelled up to 24 hours before the scheduled time.';
            if (request()->ajax() || request()->expectsJson()) {
                return response()->json(['error' => $errorMessage], 422);
            }

            return back()->with('error', $errorMessage);
        }

        $reservation->cancel();

        // send mail
        /*
        // Send email to the reservation owner
    $email = $reservation->customer_email ?? $reservation->user->email;
    Mail::to($email)->send(new ReservationCancelledMail($reservation));

    // Send email to all admins
    $adminEmails = User::where('role', 'admin')
        ->whereNotNull('email')
        ->pluck('email')
        ->toArray();

    if (!empty($adminEmails)) {
        Mail::to($adminEmails)->send(new ReservationCancelledMail($reservation));
    }
        */

        // Return JSON response for AJAX requests
        if (request()->ajax() || request()->expectsJson()) {
            // Get updated upcoming reservations count
            $upcomingCount = Reservation::upcoming()->active()->count();

            return response()->json([
                'success' => true,
                'message' => 'Reservation cancelled successfully.',
                'reservation' => [
                    'id' => $reservation->id,
                    'status' => $reservation->status,
                    'status_color' => $reservation->status_color,
                    'can_be_cancelled' => $reservation->canBeCancelled(),
                    'can_be_uncancelled' => $reservation->canBeUncancelled(),
                    'can_be_modified' => $reservation->canBeModified(),
                    'user_can_edit' => auth()->user()->isAdmin() || $reservation->user_id === auth()->id(),
                ],
                'upcoming_count' => $upcomingCount,
            ]);
        }

        return back()->with('success', 'Reservation cancelled successfully.');
    }

    /**
     * Un-cancel the specified reservation.
     */
    public function uncancel(Reservation $reservation)
    {
        $user = auth()->user();

        // Authorization check: Allow admins or reservation owners
        if (! $user->isAdmin() && $reservation->user_id !== auth()->id()) {
            if (request()->ajax() || request()->expectsJson()) {
                return response()->json(['error' => 'You can only restore your own reservations.'], 403);
            }
            abort(403, 'You can only restore your own reservations.');
        }

        if (! $reservation->isCancelled()) {
            $errorMessage = 'This reservation is not cancelled.';
            if (request()->ajax() || request()->expectsJson()) {
                return response()->json(['error' => $errorMessage], 422);
            }

            return back()->with('error', $errorMessage);
        }

        if (! $reservation->canBeUncancelled()) {
            $errorMessage = 'This reservation cannot be restored.';
            if (request()->ajax() || request()->expectsJson()) {
                return response()->json(['error' => $errorMessage], 422);
            }

            return back()->with('error', $errorMessage);
        }

        $reservation->uncancel();

        // Return JSON response for AJAX requests
        if (request()->ajax() || request()->expectsJson()) {
            // Get updated upcoming reservations count
            $upcomingCount = Reservation::upcoming()->active()->count();

            return response()->json([
                'success' => true,
                'message' => 'Reservation restored successfully.',
                'reservation' => [
                    'id' => $reservation->id,
                    'status' => $reservation->status,
                    'status_color' => $reservation->status_color,
                    'can_be_cancelled' => $reservation->canBeCancelled(),
                    'can_be_uncancelled' => $reservation->canBeUncancelled(),
                    'can_be_modified' => $reservation->canBeModified(),
                    'user_can_edit' => auth()->user()->isAdmin() || $reservation->user_id === auth()->id(),
                ],
                'upcoming_count' => $upcomingCount,
            ]);
        }

        return back()->with('success', 'Reservation restored successfully.');
    }

    /*******************************************************************************************************
     * Get field availability for AJAX requests
     */
    public function checkAvailability(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'date' => ['required', 'date'],
            'duration_hours' => ['required', 'numeric', 'min:0.5', 'max:8', 'regex:/^[0-9]+(\.[05])?$/'], // Allow half-hour increments
            'exclude_reservation_id' => ['nullable', 'integer'],
        ]);

        if ($validator->fails()) {
            return response()->json(['available' => false, 'message' => 'Invalid input', 'slots' => []]);
        }

        $field = Field::findOrFail($request->field_id);
        $durationHours = (float) $request->duration_hours;
        $excludeReservationId = $request->exclude_reservation_id;

        // Get available time slots for the duration (excluding specified reservation if provided)
        $slots = $this->availabilityService->getAvailableTimeSlots($field, $request->date, $durationHours, $excludeReservationId);

        return response()->json([
            'available' => count($slots) > 0,
            'message' => count($slots) > 0 ? count($slots).' time slots available' : 'No available time slots',
            'slots' => $slots,
        ]);
    }

    /*******************************************************************************************************
     * Get available end times for a specific start time (AJAX endpoint)
     */
    public function getAvailableEndTimes(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'date' => ['required', 'date'],
            'start_time' => ['required', 'date_format:H:i'],
            'exclude_reservation_id' => ['nullable', 'integer'],
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => 'Invalid input', 'end_times' => []]);
        }

        $field = Field::findOrFail($request->field_id);
        $date = $request->date;
        $startTime = $request->start_time;
        $excludeReservationId = $request->exclude_reservation_id;

        // Get field constraints
        $minHours = (float) ($field->min_booking_hours ?? 0.5);
        $maxHours = (float) ($field->max_booking_hours ?? 8);
        $closingTime = $field->closing_time;

        // Generate potential end times in 30-minute increments
        $endTimes = [];

        try {
            $startDate = \Carbon\Carbon::parse($startTime);
            $closingDate = \Carbon\Carbon::parse($closingTime);

            // Calculate minimum and maximum end times
            $minEndTime = $startDate->copy()->addHours($minHours);
            $maxEndTime = $startDate->copy()->addHours($maxHours);

            // Don't exceed closing time
            if ($maxEndTime->greaterThan($closingDate)) {
                $maxEndTime = $closingDate;
            }

            // Generate end time options in 30-minute increments
            $currentTime = $minEndTime->copy();

            while ($currentTime->lessThanOrEqualTo($maxEndTime)) {
                $endTimeString = $currentTime->format('H:i');

                // Check if this time slot is available (no conflicts)
                if ($this->availabilityService->isFieldAvailable($field, $date, $startTime, $endTimeString, $excludeReservationId)) {
                    $duration = $currentTime->diffInHours($startDate, true);

                    $endTimes[] = [
                        'value' => $endTimeString,
                        'text' => $endTimeString.' ('.$this->formatDuration($duration).')',
                        'duration' => $duration,
                    ];
                }

                // Add 30 minutes
                $currentTime->addMinutes(30);
            }

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error generating end times', 'end_times' => []]);
        }

        return response()->json([
            'success' => true,
            'end_times' => $endTimes,
            'message' => count($endTimes) > 0 ? count($endTimes).' end times available' : 'No available end times',
        ]);
    }

    /**
     * Format duration for display
     */
    private function formatDuration(float $hours): string
    {
        if ($hours == 1) {
            return '1 hour';
        } elseif ($hours < 1) {
            $minutes = $hours * 60;

            return $minutes.' min';
        } else {
            $wholeHours = floor($hours);
            $minutes = ($hours - $wholeHours) * 60;

            if ($minutes == 0) {
                return $wholeHours.' hours';
            } else {
                return $wholeHours.'h '.$minutes.'m';
            }
        }
    }

    /**
     * Get cost estimate for AJAX requests
     */
    public function getCostEstimate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'start_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'end_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'utilities' => ['nullable', 'array'],
            'utilities.*.id' => ['required_with:utilities', 'exists:utilities,id'],
            'utilities.*.hours' => ['required_with:utilities', 'integer', 'min:1'], // Utilities must be whole number quantities
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid input', 'validation_errors' => $validator->errors()]);
        }

        // Calculate duration from start and end times
        $startTime = Carbon::parse($request->start_time);
        $endTime = Carbon::parse($request->end_time);

        // Validate that end time is after start time
        if ($endTime <= $startTime) {
            return response()->json(['error' => 'End time must be after start time']);
        }

        $durationHours = $startTime->diffInMinutes($endTime) / 60;

        $estimate = $this->costService->getReservationEstimate(
            (int) $request->field_id,
            $durationHours,
            $request->start_time,
            $request->utilities ?? []
        );

        return response()->json($estimate);
    }
}
